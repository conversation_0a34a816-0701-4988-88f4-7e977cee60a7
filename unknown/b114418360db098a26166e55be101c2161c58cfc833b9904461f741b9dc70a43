import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { hasFeatureAccess, getTierConfig } from '@/lib/stripe';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { jinaEmbeddings } from '@/lib/embeddings/jina';
import { writeFile, unlink, readFile } from 'fs/promises';
import { join } from 'path';
import { tmpdir } from 'os';
import pdf from 'pdf-parse';

// Text splitter for chunking documents - improved for better context preservation
const textSplitter = new RecursiveCharacterTextSplitter({
  chunkSize: 1500,    // Increased from 1000 to preserve more context
  chunkOverlap: 300,  // Increased from 200 for better continuity
  separators: ['\n\n', '\n', '. ', '! ', '? ', '; ', ', ', ' ', ''],  // Better semantic splitting
});

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const configId = formData.get('configId') as string;

    if (!file || !configId) {
      return NextResponse.json({
        error: 'File and configId are required'
      }, { status: 400 });
    }

    // Check user's subscription tier for knowledge base access
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Check if user has access to knowledge base feature
    if (!hasFeatureAccess(userTier as any, 'knowledge_base')) {
      return NextResponse.json({
        error: `Knowledge base is not available on the ${userTier} plan. Please upgrade to upload documents.`
      }, { status: 403 });
    }

    // Count current documents for this user and config
    const { count: currentDocCount } = await supabase
      .from('documents')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('custom_api_config_id', configId);

    // Check document limits based on tier
    const tierConfig = getTierConfig(userTier as any);
    const maxDocs = tierConfig.limits.knowledgeBaseDocuments;

    if (maxDocs !== 999999 && (currentDocCount || 0) >= maxDocs) {
      return NextResponse.json({
        error: `You have reached the maximum number of documents (${maxDocs}) for your ${userTier} plan. Please upgrade to upload more documents.`
      }, { status: 403 });
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Unsupported file type. Please upload PDF, TXT, or MD files.'
      }, { status: 400 });
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File size too large. Maximum size is 10MB.' 
      }, { status: 400 });
    }

    console.log(`[Document Upload] Processing file: ${file.name}, type: ${file.type}, size: ${file.size}`);

    // Create temporary file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const tempFilePath = join(tmpdir(), `upload_${Date.now()}_${file.name}`);
    await writeFile(tempFilePath, buffer);

    let extractedContent = '';

    try {
      // Load document based on type
      switch (file.type) {
        case 'application/pdf':
          // Suppress pdf-parse warnings by temporarily capturing console.warn
          const originalWarn = console.warn;
          console.warn = (message: any) => {
            // Only suppress the specific font warning, allow other warnings through
            if (typeof message === 'string' && message.includes('Ran out of space in font private use area')) {
              return;
            }
            originalWarn(message);
          };

          try {
            const pdfData = await pdf(buffer, {
              // Add options to improve parsing
              max: 0 // No limit on pages
            });
            extractedContent = pdfData.text;

            // Check if we got any text
            if (!extractedContent || extractedContent.trim().length === 0) {
              console.warn('[Document Upload] No text extracted from PDF, trying alternative approach...');

              // Try with different options as fallback
              const fallbackData = await pdf(buffer, {
                max: 0
              });

              extractedContent = fallbackData.text;

              if (!extractedContent || extractedContent.trim().length === 0) {
                throw new Error('No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.');
              }
            }

            // Log some useful info about the PDF
            console.log(`[Document Upload] PDF processed: ${pdfData.numpages || 'unknown'} pages, ${extractedContent.length} characters`);
          } catch (pdfError: any) {
            console.error('[Document Upload] PDF parsing error:', pdfError);
            throw new Error(`Failed to process PDF: ${pdfError.message || 'Unknown error'}`);
          } finally {
            // Restore original console.warn
            console.warn = originalWarn;
          }
          break;

        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          // For DOCX, we'll need a different approach since we removed DocxLoader
          // For now, let's throw an error and handle this separately
          throw new Error('DOCX support temporarily disabled. Please use PDF, TXT, or MD files.');

        case 'text/plain':
        case 'text/markdown':
          extractedContent = await readFile(tempFilePath, 'utf-8');
          break;

        default:
          throw new Error('Unsupported file type');
      }

      console.log(`[Document Upload] Extracted ${extractedContent.length} characters from ${file.name}`);

      // Create a document object for text splitting
      const document = {
        pageContent: extractedContent,
        metadata: {
          source: file.name,
          type: file.type
        }
      };

      // Split document into chunks
      const chunks = await textSplitter.splitDocuments([document]);
      console.log(`[Document Upload] Split into ${chunks.length} chunks`);

      // Store document metadata in database
      const { data: documentRecord, error: docError } = await supabase
        .from('documents')
        .insert({
          user_id: user.id,
          custom_api_config_id: configId,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          content: extractedContent,
          metadata: {
            chunks_count: chunks.length,
            processing_started_at: new Date().toISOString()
          },
          status: 'processing'
        })
        .select()
        .single();

      if (docError) {
        console.error('[Document Upload] Error storing document:', docError);
        throw new Error('Failed to store document metadata');
      }

      console.log(`[Document Upload] Stored document record: ${documentRecord.id}`);

      // Test embedding generation with a small sample first
      try {
        console.log(`[Document Upload] Testing Jina embedding generation...`);
        const testEmbedding = await jinaEmbeddings.embedQuery("test");
        console.log(`[Document Upload] Jina embedding test successful (${testEmbedding.length} dimensions)`);
        console.log(`[Document Upload] Database expects 1024 dimensions, Jina v3 produces ${testEmbedding.length} dimensions`);

        if (testEmbedding.length !== 1024) {
          throw new Error(`Dimension mismatch: Database expects 1024 dimensions but Jina v3 produces ${testEmbedding.length} dimensions`);
        }
      } catch (embeddingError: any) {
        console.error(`[Document Upload] Jina embedding test failed:`, embeddingError);
        throw new Error(`Jina embedding generation failed: ${embeddingError.message}`);
      }

      // Process chunks and generate embeddings
      const chunkPromises = chunks.map(async (chunk, index) => {
        try {
          console.log(`[Document Upload] Processing chunk ${index + 1}/${chunks.length} (${chunk.pageContent.length} chars)`);

          // Generate embedding for this chunk using Jina v3
          const embedding = await jinaEmbeddings.embedQuery(chunk.pageContent);
          console.log(`[Document Upload] Generated Jina embedding for chunk ${index} (${embedding.length} dimensions)`);

          // Prepare chunk data
          const chunkData = {
            document_id: documentRecord.id,
            user_id: user.id,
            custom_api_config_id: configId,
            content: chunk.pageContent,
            metadata: {
              ...chunk.metadata,
              chunk_index: index,
              chunk_size: chunk.pageContent.length
            },
            embedding: embedding
          };

          // Store chunk with embedding
          const { data: insertedChunk, error: chunkError } = await supabase
            .from('document_chunks')
            .insert(chunkData)
            .select()
            .single();

          if (chunkError) {
            console.error(`[Document Upload] Error storing chunk ${index}:`, chunkError);
            console.error(`[Document Upload] Chunk data:`, {
              document_id: documentRecord.id,
              content_length: chunk.pageContent.length,
              embedding_length: embedding.length
            });
            throw chunkError;
          }

          console.log(`[Document Upload] Successfully stored chunk ${index} with ID: ${insertedChunk.id}`);
          return { success: true, index, chunkId: insertedChunk.id };
        } catch (error: any) {
          console.error(`[Document Upload] Error processing chunk ${index}:`, error);
          return { success: false, index, error: error.message || error };
        }
      });

      // Wait for all chunks to be processed
      const chunkResults = await Promise.all(chunkPromises);
      const successfulChunks = chunkResults.filter(r => r.success).length;
      const failedChunks = chunkResults.filter(r => !r.success).length;
      const failedChunkDetails = chunkResults.filter(r => !r.success);

      console.log(`[Document Upload] Processed ${successfulChunks}/${chunks.length} chunks successfully`);

      if (failedChunks > 0) {
        console.error(`[Document Upload] Failed chunks:`, failedChunkDetails);
      }

      // Update document status
      const finalStatus = failedChunks === 0 ? 'completed' : 'failed';
      const { error: updateError } = await supabase
        .from('documents')
        .update({
          status: finalStatus,
          metadata: {
            ...documentRecord.metadata,
            chunks_processed: successfulChunks,
            chunks_failed: failedChunks,
            processing_completed_at: new Date().toISOString(),
            ...(failedChunks > 0 && { failed_chunk_errors: failedChunkDetails.slice(0, 5) }) // Store first 5 errors
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', documentRecord.id);

      if (updateError) {
        console.error(`[Document Upload] Error updating document status:`, updateError);
      }

      // Clean up temporary file
      await unlink(tempFilePath);

      // Invalidate training cache to ensure immediate effect
      try {
        const { trainingDataCache } = await import('@/lib/cache/trainingCache');
        trainingDataCache.invalidate(configId);
        console.log(`[Document Upload] Cache invalidated for config: ${configId}`);
      } catch (error) {
        console.warn('[Document Upload] Cache invalidation failed:', error);
      }

      return NextResponse.json({
        success: true,
        document: {
          id: documentRecord.id,
          filename: file.name,
          status: finalStatus,
          chunks_processed: successfulChunks,
          chunks_total: chunks.length
        }
      });

    } catch (processingError) {
      console.error('[Document Upload] Processing error:', processingError);
      
      // Clean up temporary file
      try {
        await unlink(tempFilePath);
      } catch (unlinkError) {
        console.warn('[Document Upload] Failed to clean up temp file:', unlinkError);
      }

      throw processingError;
    }

  } catch (error: any) {
    console.error('[Document Upload] Error:', error);
    return NextResponse.json({
      error: 'Failed to process document',
      details: error.message
    }, { status: 500 });
  }
}
