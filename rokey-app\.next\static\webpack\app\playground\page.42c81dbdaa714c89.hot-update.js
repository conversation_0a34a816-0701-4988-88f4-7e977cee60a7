"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        fetchSubscriptionStatus(session.user);\n                        fetchUsageStatus(session.user);\n                    } else {\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            console.log('Fetching subscription status for user:', currentUser.id);\n            const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Subscription status API error:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    errorText\n                });\n                throw new Error(\"Failed to fetch subscription status: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('Subscription status data:', data);\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async ()=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = ()=>{\n        if (user) {\n            setLoading(true);\n            fetchSubscriptionStatus(user);\n            fetchUsageStatus(user);\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'starter':\n            return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\";\n        case 'professional':\n            return \"price_1RcfViC97XFBBUvdxwixKUdg\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});