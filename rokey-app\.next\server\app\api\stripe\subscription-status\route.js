/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/subscription-status/route";
exports.ids = ["app/api/stripe/subscription-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/subscription-status/route.ts */ \"(rsc)/./src/app/api/stripe/subscription-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/subscription-status/route\",\n        pathname: \"/api/stripe/subscription-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/subscription-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\subscription-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/subscription-status/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/stripe/subscription-status/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('userId');\n        console.log('Subscription status API called for user:', userId);\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId parameter'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get user's subscription and profile\n        const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_tier').eq('id', userId).single();\n        if (profileError) {\n            console.error('Error fetching user profile:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User profile not found'\n            }, {\n                status: 404\n            });\n        }\n        // If no subscription found, user is on free tier\n        if (subscriptionError || !subscription) {\n            const tier = profile.subscription_tier || 'free';\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: tier !== 'free',\n                tier,\n                status: null,\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: tier === 'free'\n            });\n        }\n        // Check if subscription is active\n        const isActive = subscription.status === 'active';\n        const currentPeriodEnd = new Date(subscription.current_period_end);\n        const isExpired = currentPeriodEnd < new Date();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            hasActiveSubscription: isActive && !isExpired,\n            tier: subscription.tier,\n            status: subscription.status,\n            currentPeriodEnd: subscription.current_period_end,\n            currentPeriodStart: subscription.current_period_start,\n            cancelAtPeriodEnd: subscription.cancel_at_period_end,\n            stripeCustomerId: subscription.stripe_customer_id,\n            stripeSubscriptionId: subscription.stripe_subscription_id,\n            isFree: subscription.is_free_tier || false\n        });\n    } catch (error) {\n        console.error('Error fetching subscription status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { userId } = await req.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get current usage for the user\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        const { data: usage, error: usageError } = await supabase.from('usage_tracking').select('*').eq('user_id', userId).eq('month_year', currentMonth).single();\n        // Get user's current tier\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier').eq('id', userId).single();\n        const tier = profile?.subscription_tier || 'free';\n        // Get configuration and API key counts\n        const { count: configCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        const { count: apiKeyCount } = await supabase.from('api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        // Note: Workflow features will be added in future updates\n        // Calculate tier limits\n        const limits = getTierLimits(tier);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tier,\n            usage: {\n                configurations: configCount || 0,\n                apiKeys: apiKeyCount || 0,\n                apiRequests: usage?.api_requests_count || 0\n            },\n            limits,\n            canCreateConfig: (configCount || 0) < limits.configurations,\n            canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig\n        });\n    } catch (error) {\n        console.error('Error fetching usage status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTierLimits(tier) {\n    switch(tier){\n        case 'free':\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'starter':\n            return {\n                configurations: 4,\n                apiKeysPerConfig: 5,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 3,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'professional':\n            return {\n                configurations: 20,\n                apiKeysPerConfig: 15,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 5,\n                canUseSemanticCaching: true\n            };\n        case 'enterprise':\n            return {\n                configurations: 999999,\n                apiKeysPerConfig: 999999,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 999999,\n                canUseSemanticCaching: true\n            };\n        default:\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9zdHJpcGUvc3Vic2NyaXB0aW9uLXN0YXR1cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ0g7QUFHckQsTUFBTUUsV0FBV0Qsc0dBQVlBLENBQzNCRSwwQ0FBb0MsRUFDcENBLFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCO0FBR2hDLGVBQWVDLElBQUlDLEdBQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLElBQUlHLEdBQUc7UUFDeEMsTUFBTUMsU0FBU0gsYUFBYUksR0FBRyxDQUFDO1FBRWhDQyxRQUFRQyxHQUFHLENBQUMsNENBQTRDSDtRQUV4RCxJQUFJLENBQUNBLFFBQVE7WUFDWCxPQUFPWixxREFBWUEsQ0FBQ2dCLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBMkIsR0FDcEM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHdDQUF3QztRQUN4QyxJQUFJZixRQUFRQyxHQUFHLENBQUNlLGtDQUFrQyxLQUFLLFFBQVE7WUFDN0RMLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU9mLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUFDO2dCQUN2QkksdUJBQXVCO2dCQUN2QkMsTUFBTTtnQkFDTkgsUUFBUTtnQkFDUkksa0JBQWtCO2dCQUNsQkMsbUJBQW1CO2dCQUNuQkMsUUFBUTtnQkFDUkMsVUFBVTtnQkFDVkMsU0FBUztZQUNYO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTSxFQUFFQyxNQUFNQyxZQUFZLEVBQUVYLE9BQU9ZLGlCQUFpQixFQUFFLEdBQUcsTUFBTTNCLFNBQzVENEIsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdwQixRQUNkcUIsS0FBSyxDQUFDLGNBQWM7WUFBRUMsV0FBVztRQUFNLEdBQ3ZDQyxLQUFLLENBQUMsR0FDTkMsTUFBTTtRQUVULE1BQU0sRUFBRVQsTUFBTVUsT0FBTyxFQUFFcEIsT0FBT3FCLFlBQVksRUFBRSxHQUFHLE1BQU1wQyxTQUNsRDRCLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLHFCQUNQQyxFQUFFLENBQUMsTUFBTXBCLFFBQ1R3QixNQUFNO1FBRVQsSUFBSUUsY0FBYztZQUNoQnhCLFFBQVFHLEtBQUssQ0FBQyxnQ0FBZ0NxQjtZQUM5QyxPQUFPdEMscURBQVlBLENBQUNnQixJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXlCLEdBQ2xDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxpREFBaUQ7UUFDakQsSUFBSVcscUJBQXFCLENBQUNELGNBQWM7WUFDdEMsTUFBTVAsT0FBT2dCLFFBQVFFLGlCQUFpQixJQUFJO1lBQzFDLE9BQU92QyxxREFBWUEsQ0FBQ2dCLElBQUksQ0FBQztnQkFDdkJJLHVCQUF1QkMsU0FBUztnQkFDaENBO2dCQUNBSCxRQUFRO2dCQUNSSSxrQkFBa0I7Z0JBQ2xCQyxtQkFBbUI7Z0JBQ25CQyxRQUFRSCxTQUFTO1lBQ25CO1FBQ0Y7UUFFQSxrQ0FBa0M7UUFDbEMsTUFBTW1CLFdBQVdaLGFBQWFWLE1BQU0sS0FBSztRQUN6QyxNQUFNSSxtQkFBbUIsSUFBSW1CLEtBQUtiLGFBQWFjLGtCQUFrQjtRQUNqRSxNQUFNQyxZQUFZckIsbUJBQW1CLElBQUltQjtRQUV6QyxPQUFPekMscURBQVlBLENBQUNnQixJQUFJLENBQUM7WUFDdkJJLHVCQUF1Qm9CLFlBQVksQ0FBQ0c7WUFDcEN0QixNQUFNTyxhQUFhUCxJQUFJO1lBQ3ZCSCxRQUFRVSxhQUFhVixNQUFNO1lBQzNCSSxrQkFBa0JNLGFBQWFjLGtCQUFrQjtZQUNqREUsb0JBQW9CaEIsYUFBYWlCLG9CQUFvQjtZQUNyRHRCLG1CQUFtQkssYUFBYWtCLG9CQUFvQjtZQUNwREMsa0JBQWtCbkIsYUFBYW9CLGtCQUFrQjtZQUNqREMsc0JBQXNCckIsYUFBYXNCLHNCQUFzQjtZQUN6RDFCLFFBQVFJLGFBQWF1QixZQUFZLElBQUk7UUFDdkM7SUFFRixFQUFFLE9BQU9sQyxPQUFPO1FBQ2RILFFBQVFHLEtBQUssQ0FBQyx1Q0FBdUNBO1FBRXJELHVEQUF1RDtRQUN2RCxJQUFJQSxpQkFBaUJtQyxTQUFVbkMsQ0FBQUEsTUFBTVMsT0FBTyxDQUFDMkIsUUFBUSxDQUFDLG1CQUFtQnBDLE1BQU1TLE9BQU8sQ0FBQzJCLFFBQVEsQ0FBQyxVQUFTLEdBQUk7WUFDM0d2QyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPZixxREFBWUEsQ0FBQ2dCLElBQUksQ0FBQztnQkFDdkJJLHVCQUF1QjtnQkFDdkJDLE1BQU07Z0JBQ05ILFFBQVE7Z0JBQ1JJLGtCQUFrQjtnQkFDbEJDLG1CQUFtQjtnQkFDbkJDLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtRQUNGO1FBRUEsT0FBTzFCLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZW9DLEtBQUs5QyxHQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTSxFQUFFSSxNQUFNLEVBQUUsR0FBRyxNQUFNSixJQUFJUSxJQUFJO1FBRWpDLElBQUksQ0FBQ0osUUFBUTtZQUNYLE9BQU9aLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFpQixHQUMxQjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsd0NBQXdDO1FBQ3hDLElBQUlmLFFBQVFDLEdBQUcsQ0FBQ2Usa0NBQWtDLEtBQUssUUFBUTtZQUM3REwsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT2YscURBQVlBLENBQUNnQixJQUFJLENBQUM7Z0JBQ3ZCSyxNQUFNO2dCQUNOa0MsT0FBTztvQkFDTEMsZ0JBQWdCO29CQUNoQkMsU0FBUztvQkFDVEMsYUFBYTtnQkFDZjtnQkFDQUMsUUFBUUMsY0FBYztnQkFDdEJDLGlCQUFpQjtnQkFDakJDLGlCQUFpQjtnQkFDakJyQyxVQUFVO2dCQUNWQyxTQUFTO1lBQ1g7UUFDRjtRQUVBLGlDQUFpQztRQUNqQyxNQUFNcUMsZUFBZSxJQUFJdEIsT0FBT3VCLFdBQVcsR0FBR0MsS0FBSyxDQUFDLEdBQUcsSUFBSSxpQkFBaUI7UUFFNUUsTUFBTSxFQUFFdEMsTUFBTTRCLEtBQUssRUFBRXRDLE9BQU9pRCxVQUFVLEVBQUUsR0FBRyxNQUFNaEUsU0FDOUM0QixJQUFJLENBQUMsa0JBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsV0FBV3BCLFFBQ2RvQixFQUFFLENBQUMsY0FBYytCLGNBQ2pCM0IsTUFBTTtRQUVULDBCQUEwQjtRQUMxQixNQUFNLEVBQUVULE1BQU1VLE9BQU8sRUFBRSxHQUFHLE1BQU1uQyxTQUM3QjRCLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLHFCQUNQQyxFQUFFLENBQUMsTUFBTXBCLFFBQ1R3QixNQUFNO1FBRVQsTUFBTWYsT0FBT2dCLFNBQVNFLHFCQUFxQjtRQUUzQyx1Q0FBdUM7UUFDdkMsTUFBTSxFQUFFNEIsT0FBT0MsV0FBVyxFQUFFLEdBQUcsTUFBTWxFLFNBQ2xDNEIsSUFBSSxDQUFDLHNCQUNMQyxNQUFNLENBQUMsS0FBSztZQUFFb0MsT0FBTztZQUFTRSxNQUFNO1FBQUssR0FDekNyQyxFQUFFLENBQUMsV0FBV3BCO1FBRWpCLE1BQU0sRUFBRXVELE9BQU9HLFdBQVcsRUFBRSxHQUFHLE1BQU1wRSxTQUNsQzRCLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsS0FBSztZQUFFb0MsT0FBTztZQUFTRSxNQUFNO1FBQUssR0FDekNyQyxFQUFFLENBQUMsV0FBV3BCO1FBRWpCLDBEQUEwRDtRQUUxRCx3QkFBd0I7UUFDeEIsTUFBTStDLFNBQVNDLGNBQWN2QztRQUU3QixPQUFPckIscURBQVlBLENBQUNnQixJQUFJLENBQUM7WUFDdkJLO1lBQ0FrQyxPQUFPO2dCQUNMQyxnQkFBZ0JZLGVBQWU7Z0JBQy9CWCxTQUFTYSxlQUFlO2dCQUN4QlosYUFBYUgsT0FBT2dCLHNCQUFzQjtZQUM1QztZQUNBWjtZQUNBRSxpQkFBaUIsQ0FBQ08sZUFBZSxLQUFLVCxPQUFPSCxjQUFjO1lBQzNETSxpQkFBaUIsQ0FBQ1EsZUFBZSxLQUFLWCxPQUFPYSxnQkFBZ0I7UUFDL0Q7SUFFRixFQUFFLE9BQU92RCxPQUFPO1FBQ2RILFFBQVFHLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBRTlDLHVEQUF1RDtRQUN2RCxJQUFJQSxpQkFBaUJtQyxTQUFVbkMsQ0FBQUEsTUFBTVMsT0FBTyxDQUFDMkIsUUFBUSxDQUFDLG1CQUFtQnBDLE1BQU1TLE9BQU8sQ0FBQzJCLFFBQVEsQ0FBQyxVQUFTLEdBQUk7WUFDM0d2QyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPZixxREFBWUEsQ0FBQ2dCLElBQUksQ0FBQztnQkFDdkJLLE1BQU07Z0JBQ05rQyxPQUFPO29CQUNMQyxnQkFBZ0I7b0JBQ2hCQyxTQUFTO29CQUNUQyxhQUFhO2dCQUNmO2dCQUNBQyxRQUFRQyxjQUFjO2dCQUN0QkMsaUJBQWlCO2dCQUNqQkMsaUJBQWlCO2dCQUNqQnJDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtRQUNGO1FBRUEsT0FBTzFCLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsU0FBUzBDLGNBQWN2QyxJQUFZO0lBQ2pDLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87Z0JBQ0xtQyxnQkFBZ0I7Z0JBQ2hCZ0Isa0JBQWtCO2dCQUNsQmQsYUFBYTtnQkFDYmUsdUJBQXVCO2dCQUN2QkMsbUJBQW1CO2dCQUNuQkMsZ0JBQWdCO2dCQUNoQkMseUJBQXlCO2dCQUN6QkMscUJBQXFCO2dCQUNyQkMsd0JBQXdCO2dCQUN4QkMsdUJBQXVCO1lBQ3pCO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQ0x2QixnQkFBZ0I7Z0JBQ2hCZ0Isa0JBQWtCO2dCQUNsQmQsYUFBYTtnQkFDYmUsdUJBQXVCO2dCQUN2QkMsbUJBQW1CO2dCQUNuQkMsZ0JBQWdCO2dCQUNoQkMseUJBQXlCO2dCQUN6QkMscUJBQXFCO2dCQUNyQkMsd0JBQXdCO2dCQUN4QkMsdUJBQXVCO1lBQ3pCO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQ0x2QixnQkFBZ0I7Z0JBQ2hCZ0Isa0JBQWtCO2dCQUNsQmQsYUFBYTtnQkFDYmUsdUJBQXVCO2dCQUN2QkMsbUJBQW1CO2dCQUNuQkMsZ0JBQWdCO2dCQUNoQkMseUJBQXlCO2dCQUN6QkMscUJBQXFCO2dCQUNyQkMsd0JBQXdCO2dCQUN4QkMsdUJBQXVCO1lBQ3pCO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQ0x2QixnQkFBZ0I7Z0JBQ2hCZ0Isa0JBQWtCO2dCQUNsQmQsYUFBYTtnQkFDYmUsdUJBQXVCO2dCQUN2QkMsbUJBQW1CO2dCQUNuQkMsZ0JBQWdCO2dCQUNoQkMseUJBQXlCO2dCQUN6QkMscUJBQXFCO2dCQUNyQkMsd0JBQXdCO2dCQUN4QkMsdUJBQXVCO1lBQ3pCO1FBQ0Y7WUFDRSxPQUFPO2dCQUNMdkIsZ0JBQWdCO2dCQUNoQmdCLGtCQUFrQjtnQkFDbEJkLGFBQWE7Z0JBQ2JlLHVCQUF1QjtnQkFDdkJDLG1CQUFtQjtnQkFDbkJDLGdCQUFnQjtnQkFDaEJDLHlCQUF5QjtnQkFDekJDLHFCQUFxQjtnQkFDckJDLHdCQUF3QjtnQkFDeEJDLHVCQUF1QjtZQUN6QjtJQUNKO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxhcHBcXGFwaVxcc3RyaXBlXFxzdWJzY3JpcHRpb24tc3RhdHVzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB7IFNUUklQRV9FTlZfSU5GTyB9IGZyb20gJ0AvbGliL3N0cmlwZS1jb25maWcnO1xuXG5jb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChcbiAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSFcbik7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcS51cmwpO1xuICAgIGNvbnN0IHVzZXJJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3VzZXJJZCcpO1xuXG4gICAgY29uc29sZS5sb2coJ1N1YnNjcmlwdGlvbiBzdGF0dXMgQVBJIGNhbGxlZCBmb3IgdXNlcjonLCB1c2VySWQpO1xuXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgdXNlcklkIHBhcmFtZXRlcicgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBuZXR3b3JrIGNvbm5lY3Rpdml0eSBpc3N1ZXNcbiAgICBpZiAocHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVkVSX0NPTk5FQ1RJVklUWV9JU1NVRSA9PT0gJ3RydWUnKSB7XG4gICAgICBjb25zb2xlLmxvZygnTmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWUgZGV0ZWN0ZWQsIHJldHVybmluZyBmYWxsYmFjayByZXNwb25zZScpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgaGFzQWN0aXZlU3Vic2NyaXB0aW9uOiB0cnVlLFxuICAgICAgICB0aWVyOiAnZnJlZScsXG4gICAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICAgIGN1cnJlbnRQZXJpb2RFbmQ6IG51bGwsXG4gICAgICAgIGNhbmNlbEF0UGVyaW9kRW5kOiBmYWxzZSxcbiAgICAgICAgaXNGcmVlOiB0cnVlLFxuICAgICAgICBmYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogJ1VzaW5nIGZhbGxiYWNrIGR1ZSB0byBuZXR3b3JrIGNvbm5lY3Rpdml0eSBpc3N1ZXMnXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBHZXQgdXNlcidzIHN1YnNjcmlwdGlvbiBhbmQgcHJvZmlsZVxuICAgIGNvbnN0IHsgZGF0YTogc3Vic2NyaXB0aW9uLCBlcnJvcjogc3Vic2NyaXB0aW9uRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnc3Vic2NyaXB0aW9ucycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgLmxpbWl0KDEpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBjb25zdCB7IGRhdGE6IHByb2ZpbGUsIGVycm9yOiBwcm9maWxlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuc2VsZWN0KCdzdWJzY3JpcHRpb25fdGllcicpXG4gICAgICAuZXEoJ2lkJywgdXNlcklkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHByb2ZpbGVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBwcm9maWxlOicsIHByb2ZpbGVFcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdVc2VyIHByb2ZpbGUgbm90IGZvdW5kJyB9LFxuICAgICAgICB7IHN0YXR1czogNDA0IH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gSWYgbm8gc3Vic2NyaXB0aW9uIGZvdW5kLCB1c2VyIGlzIG9uIGZyZWUgdGllclxuICAgIGlmIChzdWJzY3JpcHRpb25FcnJvciB8fCAhc3Vic2NyaXB0aW9uKSB7XG4gICAgICBjb25zdCB0aWVyID0gcHJvZmlsZS5zdWJzY3JpcHRpb25fdGllciB8fCAnZnJlZSc7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBoYXNBY3RpdmVTdWJzY3JpcHRpb246IHRpZXIgIT09ICdmcmVlJyxcbiAgICAgICAgdGllcixcbiAgICAgICAgc3RhdHVzOiBudWxsLFxuICAgICAgICBjdXJyZW50UGVyaW9kRW5kOiBudWxsLFxuICAgICAgICBjYW5jZWxBdFBlcmlvZEVuZDogZmFsc2UsXG4gICAgICAgIGlzRnJlZTogdGllciA9PT0gJ2ZyZWUnLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgc3Vic2NyaXB0aW9uIGlzIGFjdGl2ZVxuICAgIGNvbnN0IGlzQWN0aXZlID0gc3Vic2NyaXB0aW9uLnN0YXR1cyA9PT0gJ2FjdGl2ZSc7XG4gICAgY29uc3QgY3VycmVudFBlcmlvZEVuZCA9IG5ldyBEYXRlKHN1YnNjcmlwdGlvbi5jdXJyZW50X3BlcmlvZF9lbmQpO1xuICAgIGNvbnN0IGlzRXhwaXJlZCA9IGN1cnJlbnRQZXJpb2RFbmQgPCBuZXcgRGF0ZSgpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGhhc0FjdGl2ZVN1YnNjcmlwdGlvbjogaXNBY3RpdmUgJiYgIWlzRXhwaXJlZCxcbiAgICAgIHRpZXI6IHN1YnNjcmlwdGlvbi50aWVyLFxuICAgICAgc3RhdHVzOiBzdWJzY3JpcHRpb24uc3RhdHVzLFxuICAgICAgY3VycmVudFBlcmlvZEVuZDogc3Vic2NyaXB0aW9uLmN1cnJlbnRfcGVyaW9kX2VuZCxcbiAgICAgIGN1cnJlbnRQZXJpb2RTdGFydDogc3Vic2NyaXB0aW9uLmN1cnJlbnRfcGVyaW9kX3N0YXJ0LFxuICAgICAgY2FuY2VsQXRQZXJpb2RFbmQ6IHN1YnNjcmlwdGlvbi5jYW5jZWxfYXRfcGVyaW9kX2VuZCxcbiAgICAgIHN0cmlwZUN1c3RvbWVySWQ6IHN1YnNjcmlwdGlvbi5zdHJpcGVfY3VzdG9tZXJfaWQsXG4gICAgICBzdHJpcGVTdWJzY3JpcHRpb25JZDogc3Vic2NyaXB0aW9uLnN0cmlwZV9zdWJzY3JpcHRpb25faWQsXG4gICAgICBpc0ZyZWU6IHN1YnNjcmlwdGlvbi5pc19mcmVlX3RpZXIgfHwgZmFsc2UsXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdWJzY3JpcHRpb24gc3RhdHVzOicsIGVycm9yKTtcblxuICAgIC8vIElmIHRoZXJlJ3MgYSBuZXR3b3JrIGVycm9yLCByZXR1cm4gZmFsbGJhY2sgcmVzcG9uc2VcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZmV0Y2ggZmFpbGVkJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnbmV0d29yaycpKSkge1xuICAgICAgY29uc29sZS5sb2coJ05ldHdvcmsgZXJyb3IgZGV0ZWN0ZWQsIHJldHVybmluZyBmYWxsYmFjayByZXNwb25zZScpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgaGFzQWN0aXZlU3Vic2NyaXB0aW9uOiB0cnVlLFxuICAgICAgICB0aWVyOiAnZnJlZScsXG4gICAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICAgIGN1cnJlbnRQZXJpb2RFbmQ6IG51bGwsXG4gICAgICAgIGNhbmNlbEF0UGVyaW9kRW5kOiBmYWxzZSxcbiAgICAgICAgaXNGcmVlOiB0cnVlLFxuICAgICAgICBmYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogJ1VzaW5nIGZhbGxiYWNrIGR1ZSB0byBuZXR3b3JrIGVycm9yJ1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgdXNlcklkIH0gPSBhd2FpdCByZXEuanNvbigpO1xuXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgdXNlcklkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgZm9yIG5ldHdvcmsgY29ubmVjdGl2aXR5IGlzc3Vlc1xuICAgIGlmIChwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWRVJfQ09OTkVDVElWSVRZX0lTU1VFID09PSAndHJ1ZScpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdOZXR3b3JrIGNvbm5lY3Rpdml0eSBpc3N1ZSBkZXRlY3RlZCwgcmV0dXJuaW5nIGZhbGxiYWNrIHVzYWdlIHJlc3BvbnNlJyk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICB0aWVyOiAnZnJlZScsXG4gICAgICAgIHVzYWdlOiB7XG4gICAgICAgICAgY29uZmlndXJhdGlvbnM6IDAsXG4gICAgICAgICAgYXBpS2V5czogMCxcbiAgICAgICAgICBhcGlSZXF1ZXN0czogMCxcbiAgICAgICAgfSxcbiAgICAgICAgbGltaXRzOiBnZXRUaWVyTGltaXRzKCdmcmVlJyksXG4gICAgICAgIGNhbkNyZWF0ZUNvbmZpZzogdHJ1ZSxcbiAgICAgICAgY2FuQ3JlYXRlQXBpS2V5OiB0cnVlLFxuICAgICAgICBmYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogJ1VzaW5nIGZhbGxiYWNrIGR1ZSB0byBuZXR3b3JrIGNvbm5lY3Rpdml0eSBpc3N1ZXMnXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBHZXQgY3VycmVudCB1c2FnZSBmb3IgdGhlIHVzZXJcbiAgICBjb25zdCBjdXJyZW50TW9udGggPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgNyk7IC8vIFlZWVktTU0gZm9ybWF0XG5cbiAgICBjb25zdCB7IGRhdGE6IHVzYWdlLCBlcnJvcjogdXNhZ2VFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2FnZV90cmFja2luZycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5lcSgnbW9udGhfeWVhcicsIGN1cnJlbnRNb250aClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIC8vIEdldCB1c2VyJ3MgY3VycmVudCB0aWVyXG4gICAgY29uc3QgeyBkYXRhOiBwcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgLnNlbGVjdCgnc3Vic2NyaXB0aW9uX3RpZXInKVxuICAgICAgLmVxKCdpZCcsIHVzZXJJZClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGNvbnN0IHRpZXIgPSBwcm9maWxlPy5zdWJzY3JpcHRpb25fdGllciB8fCAnZnJlZSc7XG5cbiAgICAvLyBHZXQgY29uZmlndXJhdGlvbiBhbmQgQVBJIGtleSBjb3VudHNcbiAgICBjb25zdCB7IGNvdW50OiBjb25maWdDb3VudCB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjdXN0b21fYXBpX2NvbmZpZ3MnKVxuICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZCk7XG5cbiAgICBjb25zdCB7IGNvdW50OiBhcGlLZXlDb3VudCB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhcGlfa2V5cycpXG4gICAgICAuc2VsZWN0KCcqJywgeyBjb3VudDogJ2V4YWN0JywgaGVhZDogdHJ1ZSB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKTtcblxuICAgIC8vIE5vdGU6IFdvcmtmbG93IGZlYXR1cmVzIHdpbGwgYmUgYWRkZWQgaW4gZnV0dXJlIHVwZGF0ZXNcblxuICAgIC8vIENhbGN1bGF0ZSB0aWVyIGxpbWl0c1xuICAgIGNvbnN0IGxpbWl0cyA9IGdldFRpZXJMaW1pdHModGllcik7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgdGllcixcbiAgICAgIHVzYWdlOiB7XG4gICAgICAgIGNvbmZpZ3VyYXRpb25zOiBjb25maWdDb3VudCB8fCAwLFxuICAgICAgICBhcGlLZXlzOiBhcGlLZXlDb3VudCB8fCAwLFxuICAgICAgICBhcGlSZXF1ZXN0czogdXNhZ2U/LmFwaV9yZXF1ZXN0c19jb3VudCB8fCAwLFxuICAgICAgfSxcbiAgICAgIGxpbWl0cyxcbiAgICAgIGNhbkNyZWF0ZUNvbmZpZzogKGNvbmZpZ0NvdW50IHx8IDApIDwgbGltaXRzLmNvbmZpZ3VyYXRpb25zLFxuICAgICAgY2FuQ3JlYXRlQXBpS2V5OiAoYXBpS2V5Q291bnQgfHwgMCkgPCBsaW1pdHMuYXBpS2V5c1BlckNvbmZpZyxcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzYWdlIHN0YXR1czonLCBlcnJvcik7XG5cbiAgICAvLyBJZiB0aGVyZSdzIGEgbmV0d29yayBlcnJvciwgcmV0dXJuIGZhbGxiYWNrIHJlc3BvbnNlXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2ZldGNoIGZhaWxlZCcpIHx8IGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25ldHdvcmsnKSkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdOZXR3b3JrIGVycm9yIGRldGVjdGVkLCByZXR1cm5pbmcgZmFsbGJhY2sgdXNhZ2UgcmVzcG9uc2UnKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHRpZXI6ICdmcmVlJyxcbiAgICAgICAgdXNhZ2U6IHtcbiAgICAgICAgICBjb25maWd1cmF0aW9uczogMCxcbiAgICAgICAgICBhcGlLZXlzOiAwLFxuICAgICAgICAgIGFwaVJlcXVlc3RzOiAwLFxuICAgICAgICB9LFxuICAgICAgICBsaW1pdHM6IGdldFRpZXJMaW1pdHMoJ2ZyZWUnKSxcbiAgICAgICAgY2FuQ3JlYXRlQ29uZmlnOiB0cnVlLFxuICAgICAgICBjYW5DcmVhdGVBcGlLZXk6IHRydWUsXG4gICAgICAgIGZhbGxiYWNrOiB0cnVlLFxuICAgICAgICBtZXNzYWdlOiAnVXNpbmcgZmFsbGJhY2sgZHVlIHRvIG5ldHdvcmsgZXJyb3InXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBnZXRUaWVyTGltaXRzKHRpZXI6IHN0cmluZykge1xuICBzd2l0Y2ggKHRpZXIpIHtcbiAgICBjYXNlICdmcmVlJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvbmZpZ3VyYXRpb25zOiAxLFxuICAgICAgICBhcGlLZXlzUGVyQ29uZmlnOiAzLFxuICAgICAgICBhcGlSZXF1ZXN0czogOTk5OTk5LCAvLyBVbmxpbWl0ZWRcbiAgICAgICAgY2FuVXNlQWR2YW5jZWRSb3V0aW5nOiBmYWxzZSxcbiAgICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IGZhbHNlLFxuICAgICAgICBtYXhDdXN0b21Sb2xlczogMCxcbiAgICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IGZhbHNlLFxuICAgICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBmYWxzZSxcbiAgICAgICAga25vd2xlZGdlQmFzZURvY3VtZW50czogMCxcbiAgICAgICAgY2FuVXNlU2VtYW50aWNDYWNoaW5nOiBmYWxzZSxcbiAgICAgIH07XG4gICAgY2FzZSAnc3RhcnRlcic6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb25maWd1cmF0aW9uczogNCxcbiAgICAgICAgYXBpS2V5c1BlckNvbmZpZzogNSxcbiAgICAgICAgYXBpUmVxdWVzdHM6IDk5OTk5OSwgLy8gVW5saW1pdGVkXG4gICAgICAgIGNhblVzZUFkdmFuY2VkUm91dGluZzogdHJ1ZSxcbiAgICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IHRydWUsXG4gICAgICAgIG1heEN1c3RvbVJvbGVzOiAzLFxuICAgICAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogdHJ1ZSxcbiAgICAgICAgY2FuVXNlS25vd2xlZGdlQmFzZTogZmFsc2UsXG4gICAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDAsXG4gICAgICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogZmFsc2UsXG4gICAgICB9O1xuICAgIGNhc2UgJ3Byb2Zlc3Npb25hbCc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb25maWd1cmF0aW9uczogMjAsXG4gICAgICAgIGFwaUtleXNQZXJDb25maWc6IDE1LFxuICAgICAgICBhcGlSZXF1ZXN0czogOTk5OTk5LCAvLyBVbmxpbWl0ZWRcbiAgICAgICAgY2FuVXNlQWR2YW5jZWRSb3V0aW5nOiB0cnVlLFxuICAgICAgICBjYW5Vc2VDdXN0b21Sb2xlczogdHJ1ZSxcbiAgICAgICAgbWF4Q3VzdG9tUm9sZXM6IDk5OTk5OSxcbiAgICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IHRydWUsXG4gICAgICAgIGNhblVzZUtub3dsZWRnZUJhc2U6IHRydWUsXG4gICAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDUsXG4gICAgICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogdHJ1ZSxcbiAgICAgIH07XG4gICAgY2FzZSAnZW50ZXJwcmlzZSc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb25maWd1cmF0aW9uczogOTk5OTk5LCAvLyBVbmxpbWl0ZWRcbiAgICAgICAgYXBpS2V5c1BlckNvbmZpZzogOTk5OTk5LCAvLyBVbmxpbWl0ZWRcbiAgICAgICAgYXBpUmVxdWVzdHM6IDk5OTk5OSwgLy8gVW5saW1pdGVkXG4gICAgICAgIGNhblVzZUFkdmFuY2VkUm91dGluZzogdHJ1ZSxcbiAgICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IHRydWUsXG4gICAgICAgIG1heEN1c3RvbVJvbGVzOiA5OTk5OTksXG4gICAgICAgIGNhblVzZVByb21wdEVuZ2luZWVyaW5nOiB0cnVlLFxuICAgICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiB0cnVlLFxuICAgICAgICBrbm93bGVkZ2VCYXNlRG9jdW1lbnRzOiA5OTk5OTksXG4gICAgICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogdHJ1ZSxcbiAgICAgIH07XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvbmZpZ3VyYXRpb25zOiAxLFxuICAgICAgICBhcGlLZXlzUGVyQ29uZmlnOiAzLFxuICAgICAgICBhcGlSZXF1ZXN0czogOTk5OTk5LFxuICAgICAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IGZhbHNlLFxuICAgICAgICBjYW5Vc2VDdXN0b21Sb2xlczogZmFsc2UsXG4gICAgICAgIG1heEN1c3RvbVJvbGVzOiAwLFxuICAgICAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogZmFsc2UsXG4gICAgICAgIGNhblVzZUtub3dsZWRnZUJhc2U6IGZhbHNlLFxuICAgICAgICBrbm93bGVkZ2VCYXNlRG9jdW1lbnRzOiAwLFxuICAgICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGZhbHNlLFxuICAgICAgfTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJHRVQiLCJyZXEiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJ1c2VySWQiLCJnZXQiLCJjb25zb2xlIiwibG9nIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiU1VQQUJBU0VfU0VSVkVSX0NPTk5FQ1RJVklUWV9JU1NVRSIsImhhc0FjdGl2ZVN1YnNjcmlwdGlvbiIsInRpZXIiLCJjdXJyZW50UGVyaW9kRW5kIiwiY2FuY2VsQXRQZXJpb2RFbmQiLCJpc0ZyZWUiLCJmYWxsYmFjayIsIm1lc3NhZ2UiLCJkYXRhIiwic3Vic2NyaXB0aW9uIiwic3Vic2NyaXB0aW9uRXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJvcmRlciIsImFzY2VuZGluZyIsImxpbWl0Iiwic2luZ2xlIiwicHJvZmlsZSIsInByb2ZpbGVFcnJvciIsInN1YnNjcmlwdGlvbl90aWVyIiwiaXNBY3RpdmUiLCJEYXRlIiwiY3VycmVudF9wZXJpb2RfZW5kIiwiaXNFeHBpcmVkIiwiY3VycmVudFBlcmlvZFN0YXJ0IiwiY3VycmVudF9wZXJpb2Rfc3RhcnQiLCJjYW5jZWxfYXRfcGVyaW9kX2VuZCIsInN0cmlwZUN1c3RvbWVySWQiLCJzdHJpcGVfY3VzdG9tZXJfaWQiLCJzdHJpcGVTdWJzY3JpcHRpb25JZCIsInN0cmlwZV9zdWJzY3JpcHRpb25faWQiLCJpc19mcmVlX3RpZXIiLCJFcnJvciIsImluY2x1ZGVzIiwiUE9TVCIsInVzYWdlIiwiY29uZmlndXJhdGlvbnMiLCJhcGlLZXlzIiwiYXBpUmVxdWVzdHMiLCJsaW1pdHMiLCJnZXRUaWVyTGltaXRzIiwiY2FuQ3JlYXRlQ29uZmlnIiwiY2FuQ3JlYXRlQXBpS2V5IiwiY3VycmVudE1vbnRoIiwidG9JU09TdHJpbmciLCJzbGljZSIsInVzYWdlRXJyb3IiLCJjb3VudCIsImNvbmZpZ0NvdW50IiwiaGVhZCIsImFwaUtleUNvdW50IiwiYXBpX3JlcXVlc3RzX2NvdW50IiwiYXBpS2V5c1BlckNvbmZpZyIsImNhblVzZUFkdmFuY2VkUm91dGluZyIsImNhblVzZUN1c3RvbVJvbGVzIiwibWF4Q3VzdG9tUm9sZXMiLCJjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZyIsImNhblVzZUtub3dsZWRnZUJhc2UiLCJrbm93bGVkZ2VCYXNlRG9jdW1lbnRzIiwiY2FuVXNlU2VtYW50aWNDYWNoaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/subscription-status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();