import Stripe from 'stripe';
import { STRIPE_KEYS, STRIPE_PRICE_IDS, STRIPE_PRODUCT_IDS } from './stripe-config';

export const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

export const STRIPE_CONFIG = {
  PRICE_IDS: STRIPE_PRICE_IDS,
  PRODUCT_IDS: STRIPE_PRODUCT_IDS,
};

export type SubscriptionTier = 'free' | 'starter' | 'professional' | 'enterprise';

export interface TierConfig {
  name: string;
  price: string;
  priceId: string | null; // null for free tier
  productId: string | null; // null for free tier
  features: string[];
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
    canUseAdvancedRouting: boolean;
    canUseCustomRoles: boolean;
    maxCustomRoles: number;
    canUsePromptEngineering: boolean;
    canUseKnowledgeBase: boolean;
    knowledgeBaseDocuments: number;
    canUseSemanticCaching: boolean;
  };
}

export const TIER_CONFIGS: Record<SubscriptionTier, TierConfig> = {
  free: {
    name: 'Free',
    price: '$0',
    priceId: STRIPE_CONFIG.PRICE_IDS.FREE,
    productId: STRIPE_CONFIG.PRODUCT_IDS.FREE,
    features: [
      'Unlimited API requests',
      '1 Custom Configuration',
      '3 API Keys per config',
      'All 300+ AI models',
      'Strict fallback routing only',
      'Basic analytics only',
      'No custom roles, basic router only',
      'No prompt engineering',
      'Limited logs',
      'Community support',
    ],
    limits: {
      configurations: 1,
      apiKeysPerConfig: 3,
      apiRequests: 999999,
      canUseAdvancedRouting: false,
      canUseCustomRoles: false,
      maxCustomRoles: 0,
      canUsePromptEngineering: false,
      canUseKnowledgeBase: false,
      knowledgeBaseDocuments: 0,
      canUseSemanticCaching: false,
    },
  },
  starter: {
    name: 'Starter',
    price: '$19',
    priceId: STRIPE_CONFIG.PRICE_IDS.STARTER,
    productId: STRIPE_CONFIG.PRODUCT_IDS.STARTER,
    features: [
      'Unlimited API requests',
      '4 Custom Configurations',
      '5 API Keys per config',
      'All 300+ AI models',
      'Strict fallback + Complex routing (1 config limit)',
      'Up to 3 custom roles',
      'Intelligent role routing (1 config)',
      'Prompt engineering (no file upload)',
      'Enhanced logs and analytics',
      'Community support',
    ],
    limits: {
      configurations: 4,
      apiKeysPerConfig: 5,
      apiRequests: 999999,
      canUseAdvancedRouting: true,
      canUseCustomRoles: true,
      maxCustomRoles: 3,
      canUsePromptEngineering: true,
      canUseKnowledgeBase: false,
      knowledgeBaseDocuments: 0,
      canUseSemanticCaching: false,
    },
  },
  professional: {
    name: 'Professional',
    price: '$49',
    priceId: STRIPE_CONFIG.PRICE_IDS.PROFESSIONAL,
    productId: STRIPE_CONFIG.PRODUCT_IDS.PROFESSIONAL,
    features: [
      'Unlimited API requests',
      '20 Custom Configurations',
      '15 API Keys per config',
      'All 300+ AI models',
      'All advanced routing strategies',
      'Unlimited custom roles',
      'Prompt engineering + Knowledge base (5 documents)',
      'Semantic caching',
      'Advanced analytics and logging',
      'Priority email support',
    ],
    limits: {
      configurations: 20,
      apiKeysPerConfig: 15,
      apiRequests: 999999,
      canUseAdvancedRouting: true,
      canUseCustomRoles: true,
      maxCustomRoles: 999999,
      canUsePromptEngineering: true,
      canUseKnowledgeBase: true,
      knowledgeBaseDocuments: 5,
      canUseSemanticCaching: true,
    },
  },
  enterprise: {
    name: 'Enterprise',
    price: '$149',
    priceId: STRIPE_CONFIG.PRICE_IDS.ENTERPRISE,
    productId: STRIPE_CONFIG.PRODUCT_IDS.ENTERPRISE,
    features: [
      'Unlimited API requests',
      'Unlimited configurations',
      'Unlimited API keys',
      'All 300+ models + priority access',
      'All routing strategies',
      'Unlimited custom roles',
      'All features + priority support',
      'Unlimited knowledge base documents',
      'Advanced semantic caching',
      'Custom integrations',
      'Dedicated support + phone',
      'SLA guarantee',
    ],
    limits: {
      configurations: 999999,
      apiKeysPerConfig: 999999,
      apiRequests: 999999,
      canUseAdvancedRouting: true,
      canUseCustomRoles: true,
      maxCustomRoles: 999999,
      canUsePromptEngineering: true,
      canUseKnowledgeBase: true,
      knowledgeBaseDocuments: 999999,
      canUseSemanticCaching: true,
    },
  },
};

export function getTierConfig(tier: SubscriptionTier): TierConfig {
  return TIER_CONFIGS[tier];
}

export function getPriceIdForTier(tier: SubscriptionTier): string | null {
  return TIER_CONFIGS[tier].priceId;
}

export function getTierFromPriceId(priceId: string): SubscriptionTier {
  for (const [tier, config] of Object.entries(TIER_CONFIGS)) {
    if (config.priceId === priceId) {
      return tier as SubscriptionTier;
    }
  }
  return 'free'; // Default fallback to free tier
}

export function formatPrice(tier: SubscriptionTier): string {
  return TIER_CONFIGS[tier].price;
}

export function canPerformAction(
  tier: SubscriptionTier,
  action: 'create_config' | 'create_api_key',
  currentCount: number
): boolean {
  const limits = TIER_CONFIGS[tier].limits;

  switch (action) {
    case 'create_config':
      return currentCount < limits.configurations;
    case 'create_api_key':
      return currentCount < limits.apiKeysPerConfig;
    default:
      return true;
  }
}

export function hasFeatureAccess(
  tier: SubscriptionTier,
  feature: 'custom_roles' | 'knowledge_base' | 'advanced_routing' | 'prompt_engineering' | 'semantic_caching'
): boolean {
  const limits = TIER_CONFIGS[tier].limits;

  switch (feature) {
    case 'custom_roles':
      return limits.canUseCustomRoles;
    case 'knowledge_base':
      return limits.canUseKnowledgeBase;
    case 'advanced_routing':
      return limits.canUseAdvancedRouting;
    case 'prompt_engineering':
      return limits.canUsePromptEngineering;
    case 'semantic_caching':
      return limits.canUseSemanticCaching;
    default:
      return false;
  }
}

export interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  tier: SubscriptionTier;
  status: string | null;
  currentPeriodEnd: string | null;
  cancelAtPeriodEnd: boolean;
  isFree: boolean;
}

export interface UsageStatus {
  tier: SubscriptionTier;
  usage: {
    configurations: number;
    apiKeys: number;
    apiRequests: number;
  };
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
    canUseAdvancedRouting: boolean;
    canUseCustomRoles: boolean;
    maxCustomRoles: number;
    canUsePromptEngineering: boolean;
    canUseKnowledgeBase: boolean;
    knowledgeBaseDocuments: number;
    canUseSemanticCaching: boolean;
  };
  canCreateConfig: boolean;
  canCreateApiKey: boolean;
}
