/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/create-checkout-session/route";
exports.ids = ["app/api/stripe/create-checkout-session/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-checkout-session/route\",\n        pathname: \"/api/stripe/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\create-checkout-session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/create-checkout-session/route.ts":
/*!*************************************************************!*\
  !*** ./src/app/api/stripe/create-checkout-session/route.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](_lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_KEYS.secretKey, {\n    apiVersion: '2025-02-24.acacia'\n});\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(req) {\n    try {\n        const { priceId, userId, userEmail, tier, signup, pendingUserData } = await req.json();\n        // For signup flow, we don't need userId yet\n        if (signup) {\n            // Validate required fields for signup\n            if (!priceId || !userEmail || !tier || !pendingUserData) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Missing required fields for signup: priceId, userEmail, tier, pendingUserData'\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // Validate required fields for existing user\n            if (!priceId || !userId || !userEmail || !tier) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Missing required fields: priceId, userId, userEmail, tier'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate tier\n        if (![\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(tier)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid tier. Must be starter, professional, or enterprise'\n            }, {\n                status: 400\n            });\n        }\n        // Validate price ID matches tier\n        const expectedPriceId = getPriceIdForTier(tier);\n        if (priceId !== expectedPriceId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Price ID does not match selected tier'\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already has an active subscription (only for existing users)\n        if (!signup && userId) {\n            const { data: existingSubscription } = await supabase.from('subscriptions').select('*').eq('user_id', userId).eq('status', 'active').single();\n            if (existingSubscription) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'User already has an active subscription'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Create or retrieve Stripe customer\n        let customer;\n        // First, try to find existing customer by email\n        const existingCustomers = await stripe.customers.list({\n            email: userEmail,\n            limit: 1\n        });\n        if (existingCustomers.data.length > 0) {\n            customer = existingCustomers.data[0];\n        } else {\n            // Create new customer\n            customer = await stripe.customers.create({\n                email: userEmail,\n                metadata: {\n                    user_id: userId\n                }\n            });\n        }\n        // Create checkout session\n        const sessionParams = {\n            customer: customer.id,\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: 'subscription',\n            success_url: `${ true ? 'http://localhost:3000' : 0}/auth/callback?session_id={CHECKOUT_SESSION_ID}&payment_success=true&redirectTo=${encodeURIComponent('/dashboard')}`,\n            cancel_url: `${ true ? 'http://localhost:3000' : 0}/pricing?plan=${tier}&payment_cancelled=true`,\n            metadata: {\n                user_id: userId || 'pending_signup',\n                tier: tier,\n                signup: signup ? 'true' : 'false',\n                pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined\n            },\n            subscription_data: {\n                metadata: {\n                    user_id: userId || 'pending_signup',\n                    tier: tier,\n                    signup: signup ? 'true' : 'false',\n                    pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined\n                }\n            },\n            allow_promotion_codes: true,\n            billing_address_collection: 'required',\n            customer_update: {\n                address: 'auto',\n                name: 'auto'\n            }\n        };\n        const session = await stripe.checkout.sessions.create(sessionParams);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            sessionId: session.id,\n            url: session.url\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        if (error instanceof stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"].errors.StripeError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Stripe error: ${error.message}`\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.FREE;\n        case 'starter':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.STARTER;\n        case 'professional':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.PROFESSIONAL;\n        case 'enterprise':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.ENTERPRISE;\n        default:\n            throw new Error(`Invalid tier: ${tier}`);\n    }\n}\n// Helper function to get tier display names\nfunction getTierDisplayName(tier) {\n    switch(tier){\n        case 'free':\n            return 'Free';\n        case 'starter':\n            return 'Starter';\n        case 'professional':\n            return 'Professional';\n        case 'enterprise':\n            return 'Enterprise';\n        default:\n            return 'Unknown';\n    }\n}\n// Helper function to get tier prices\nfunction getTierPrice(tier) {\n    switch(tier){\n        case 'free':\n            return '$0';\n        case 'starter':\n            return '$19';\n        case 'professional':\n            return '$49';\n        case 'enterprise':\n            return '$149';\n        default:\n            return 'Unknown';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey.substring(0, 20) + '...',\n        secret: STRIPE_KEYS.secretKey.substring(0, 20) + '...'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();