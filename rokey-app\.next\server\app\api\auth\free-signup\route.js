/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/free-signup/route";
exports.ids = ["app/api/auth/free-signup/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-signup%2Froute&page=%2Fapi%2Fauth%2Ffree-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-signup%2Froute&page=%2Fapi%2Fauth%2Ffree-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_free_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/free-signup/route.ts */ \"(rsc)/./src/app/api/auth/free-signup/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/free-signup/route\",\n        pathname: \"/api/auth/free-signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/free-signup/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\free-signup\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_free_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-signup%2Froute&page=%2Fapi%2Fauth%2Ffree-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/free-signup/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/auth/free-signup/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(req) {\n    try {\n        const { email, password, fullName } = await req.json();\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email and password are required'\n            }, {\n                status: 400\n            });\n        }\n        // Create user account\n        const { data: authData, error: authError } = await supabase.auth.admin.createUser({\n            email,\n            password,\n            email_confirm: true,\n            user_metadata: {\n                full_name: fullName || ''\n            }\n        });\n        if (authError) {\n            console.error('Error creating user:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: authError.message\n            }, {\n                status: 400\n            });\n        }\n        if (!authData.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create user'\n            }, {\n                status: 500\n            });\n        }\n        // Create user profile with free tier\n        const { error: profileError } = await supabase.from('user_profiles').insert({\n            id: authData.user.id,\n            email: authData.user.email,\n            full_name: fullName || '',\n            subscription_tier: 'free',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        });\n        if (profileError) {\n            console.error('Error creating user profile:', profileError);\n        // Don't fail the signup if profile creation fails, we can retry later\n        }\n        // Create a free tier \"subscription\" record for tracking\n        const { error: subscriptionError } = await supabase.from('subscriptions').insert({\n            user_id: authData.user.id,\n            tier: 'free',\n            status: 'active',\n            is_free_tier: true,\n            current_period_start: new Date().toISOString(),\n            current_period_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),\n            cancel_at_period_end: false,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        });\n        if (subscriptionError) {\n            console.error('Error creating free subscription record:', subscriptionError);\n        // Don't fail the signup if subscription record creation fails\n        }\n        // Initialize workflow usage tracking for current month\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        const { error: usageError } = await supabase.from('workflow_usage').insert({\n            user_id: authData.user.id,\n            month_year: currentMonth,\n            executions_count: 0,\n            active_workflows_count: 0,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        });\n        if (usageError) {\n            console.error('Error creating workflow usage record:', usageError);\n        // Don't fail the signup if usage tracking creation fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                id: authData.user.id,\n                email: authData.user.email,\n                tier: 'free'\n            },\n            message: 'Free account created successfully'\n        });\n    } catch (error) {\n        console.error('Error in free signup:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/free-signup/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-signup%2Froute&page=%2Fapi%2Fauth%2Ffree-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();