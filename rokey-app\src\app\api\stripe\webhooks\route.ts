import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_KEYS, STRIPE_PRICE_IDS } from '@/lib/stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  return new Response('Stripe webhook endpoint is active', {
    status: 200,
    headers: { 'Content-Type': 'text/plain' }
  });
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature');

  if (!signature) {
    console.error('Missing stripe-signature header');
    return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      STRIPE_KEYS.webhookSecret
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  console.log('Received webhook event:', event.type);

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      case 'checkout.session.expired':
        await handleCheckoutExpired(event.data.object as Stripe.Checkout.Session);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('Processing checkout completed:', session.id);

  if (!session.customer || !session.subscription) {
    console.error('Missing customer or subscription in checkout session');
    return;
  }

  // Get the subscription details
  const subscription = await stripe.subscriptions.retrieve(session.subscription as string);

  // Check if this is a signup flow
  const isSignup = session.metadata?.signup === 'true';
  const userId = session.metadata?.user_id;

  let finalUserId = userId;

  if (isSignup && session.metadata?.pending_user_data) {
    // This is a new signup - create the Supabase user now
    console.log('Creating Supabase user after successful payment...');

    try {
      const pendingUserData = JSON.parse(session.metadata.pending_user_data);

      // Create the Supabase user with admin privileges
      const { data: newUser, error: userError } = await supabase.auth.admin.createUser({
        email: pendingUserData.email,
        password: pendingUserData.password,
        email_confirm: true, // Auto-confirm email since payment was successful
        user_metadata: {
          full_name: `${pendingUserData.firstName} ${pendingUserData.lastName}`,
          first_name: pendingUserData.firstName,
          last_name: pendingUserData.lastName,
          selected_plan: pendingUserData.plan,
        }
      });

      if (userError) {
        console.error('Error creating Supabase user:', userError);
        throw userError;
      }

      finalUserId = newUser.user.id;
      console.log('Supabase user created successfully:', finalUserId);

    } catch (error) {
      console.error('Error processing signup after payment:', error);
      throw error;
    }
  }

  if (!finalUserId) {
    console.error('Missing user_id in session metadata');
    return;
  }

  // Determine tier from price ID
  const priceId = subscription.items.data[0]?.price.id;
  const tier = getTierFromPriceId(priceId);

  // Create subscription record
  const { error } = await supabase
    .from('subscriptions')
    .insert({
      user_id: finalUserId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      tier,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
    });

  if (error) {
    console.error('Error creating subscription record:', error);
    throw error;
  }

  // Create or update user profile with subscription tier
  const { error: profileError } = await supabase
    .from('user_profiles')
    .upsert({
      id: finalUserId,
      subscription_tier: tier,
      subscription_status: 'active',
      updated_at: new Date().toISOString(),
    });

  if (profileError) {
    console.error('Error creating/updating user profile:', profileError);
  }

  console.log(`Subscription created for user ${finalUserId} with tier ${tier}`);
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Processing subscription created:', subscription.id);
  // This is usually handled by checkout.session.completed
  // But we can add additional logic here if needed
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Processing subscription updated:', subscription.id);

  const priceId = subscription.items.data[0]?.price.id;
  const tier = getTierFromPriceId(priceId);

  const { error } = await supabase
    .from('subscriptions')
    .update({
      tier,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  if (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }

  // Update user profile tier
  const { data: subscriptionData } = await supabase
    .from('subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single();

  if (subscriptionData) {
    await supabase
      .from('user_profiles')
      .update({ subscription_tier: tier })
      .eq('id', subscriptionData.user_id);
  }

  console.log(`Subscription ${subscription.id} updated to tier ${tier}`);
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Processing subscription deleted:', subscription.id);

  const { error } = await supabase
    .from('subscriptions')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  if (error) {
    console.error('Error updating deleted subscription:', error);
    throw error;
  }

  // Update user profile to starter tier
  const { data: subscriptionData } = await supabase
    .from('subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single();

  if (subscriptionData) {
    await supabase
      .from('user_profiles')
      .update({ subscription_tier: 'starter' })
      .eq('id', subscriptionData.user_id);
  }

  console.log(`Subscription ${subscription.id} canceled`);
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Processing payment succeeded:', invoice.id);
  
  if (invoice.subscription) {
    // Update subscription status to active
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', invoice.subscription);

    if (error) {
      console.error('Error updating subscription after payment:', error);
    }
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Processing payment failed:', invoice.id);

  if (invoice.subscription) {
    // Update subscription status
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', invoice.subscription);

    if (error) {
      console.error('Error updating subscription after failed payment:', error);
    }
  }
}

async function handleCheckoutExpired(session: Stripe.Checkout.Session) {
  console.log('Processing checkout expired:', session.id);

  // Check if this was a new signup that didn't complete payment
  const userId = session.metadata?.user_id;

  if (userId && userId !== 'pending_signup') {
    console.log('Deleting user who abandoned checkout:', userId);

    try {
      // Delete the user from Supabase since they didn't complete payment
      const { error } = await supabase.auth.admin.deleteUser(userId);

      if (error) {
        console.error('Error deleting abandoned user:', error);
      } else {
        console.log('Successfully deleted abandoned user:', userId);
      }
    } catch (error) {
      console.error('Error in checkout expiry cleanup:', error);
    }
  }

  // Also run general cleanup of old pending users
  try {
    console.log('Running general cleanup of pending users...');
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/cleanup/pending-users`, {
      method: 'POST'
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Cleanup result:', result);
    }
  } catch (error) {
    console.error('Error running general cleanup:', error);
  }
}

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}
