"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createSupabaseBrowserClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            // Listen for auth state changes to handle recent sign-ins\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"ActualCheckoutContent.useEffect\": async (event, session)=>{\n                    console.log('Auth state change detected:', event, !!session);\n                    if (event === 'SIGNED_IN' && session) {\n                        console.log('User just signed in, proceeding with checkout...');\n                        // Small delay to ensure session is fully established\n                        setTimeout({\n                            \"ActualCheckoutContent.useEffect\": ()=>{\n                                initializeCheckout();\n                            }\n                        }[\"ActualCheckoutContent.useEffect\"], 100);\n                    }\n                }\n            }[\"ActualCheckoutContent.useEffect\"]);\n            initializeCheckout();\n            // Cleanup subscription\n            return ({\n                \"ActualCheckoutContent.useEffect\": ()=>subscription.unsubscribe()\n            })[\"ActualCheckoutContent.useEffect\"];\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Try to get user with retry logic for recent sign-ins (more secure than getSession)\n            let user = null;\n            let authError = null;\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(!user && retryCount < maxRetries){\n                const result = await supabase.auth.getUser();\n                user = result.data.user;\n                authError = result.error;\n                if (!user && retryCount < maxRetries - 1) {\n                    console.log(\"User not found, retrying in 1 second... (attempt \".concat(retryCount + 1, \"/\").concat(maxRetries, \")\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                }\n                retryCount++;\n            }\n            console.log('User check:', {\n                hasUser: !!user,\n                error: authError,\n                retryCount\n            });\n            // Debug user details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_CHECK',\n                    hasUser: !!user,\n                    authError: authError === null || authError === void 0 ? void 0 : authError.message,\n                    userId: user === null || user === void 0 ? void 0 : user.id,\n                    userEmail: user === null || user === void 0 ? void 0 : user.email,\n                    retryCount: retryCount,\n                    maxRetries: maxRetries\n                })\n            }).catch(()=>{});\n            if (authError || !user) {\n                console.log('No valid user found after', retryCount, 'attempts');\n                console.log('Auth error:', authError);\n                console.log('User data:', user);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'AUTH_FAILED',\n                        error: (authError === null || authError === void 0 ? void 0 : authError.message) || 'No user found',\n                        userId: userId,\n                        email: email,\n                        hasUserId: !!userId,\n                        hasEmail: !!email,\n                        retryCount: retryCount,\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                // If we have a userId from URL params, this means user was created but not signed in\n                // We need to prompt them to sign in with their credentials\n                if (userId) {\n                    setError('Please sign in with your account credentials to complete checkout. Click \"Try Again\" to go to sign in page.');\n                // Don't auto-redirect to prevent loops\n                // const signinUrl = `/auth/signin?plan=${selectedPlan}&checkout_user_id=${userId}${email ? `&email=${encodeURIComponent(email)}` : ''}`;\n                // setTimeout(() => router.push(signinUrl), 3000);\n                } else {\n                    setError('Authentication required. Please sign up first. Click \"Try Again\" to go to sign up page.');\n                // Don't auto-redirect to prevent loops\n                // setTimeout(() => router.push(`/auth/signup?plan=${selectedPlan}`), 3000);\n                }\n                return;\n            }\n            setUser(user);\n            console.log('Set user state:', user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_FOUND',\n                    userId: user.id,\n                    email: user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the user directly instead of relying on state\n                await createCheckoutSession(user.id, user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError instanceof Error ? checkoutError.message : String(checkoutError),\n                        stack: checkoutError instanceof Error ? checkoutError.stack : undefined\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error instanceof Error ? error.message : String(error)\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RcfWSC97XFBBUvdYHXbBvQ6\",\n                hasProfessionalPrice: !!\"price_1RcfViC97XFBBUvdxwixKUdg\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\" || 0;\n            case 'professional':\n                return \"price_1RcfViC97XFBBUvdxwixKUdg\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RcfViC97XFBBUvdxwixKUdg\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 435,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 434,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 490,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});