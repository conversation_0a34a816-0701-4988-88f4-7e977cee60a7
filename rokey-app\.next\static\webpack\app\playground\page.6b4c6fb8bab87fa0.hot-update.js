"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        fetchSubscriptionStatus(session.user);\n                        fetchUsageStatus(session.user);\n                    } else {\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id));\n            if (!response.ok) {\n                throw new Error('Failed to fetch subscription status');\n            }\n            const data = await response.json();\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async ()=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = ()=>{\n        if (user) {\n            setLoading(true);\n            fetchSubscriptionStatus(user);\n            fetchUsageStatus(user);\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'starter':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'professional':\n            return \"price_1RaABVC97XFBBUvdkZZc1oQB\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});